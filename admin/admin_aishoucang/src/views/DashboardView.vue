<template>
  <div class="dashboard-container">
    <el-container>
      <!-- 头部导航栏 -->
      <el-header class="header">
        <div class="header-left">
          <div class="logo-section">
            <el-icon class="logo-icon" size="28"><DataBoard /></el-icon>
            <h1 class="logo-text">爱收藏管理后台</h1>
          </div>
        </div>
        <div class="header-center">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>首页</el-breadcrumb-item>
            <el-breadcrumb-item>仪表盘</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <div class="header-actions">
            <el-tooltip content="消息通知" placement="bottom">
              <el-badge :value="3" class="notification-badge">
                <el-button circle size="large" class="action-btn">
                  <el-icon><Bell /></el-icon>
                </el-button>
              </el-badge>
            </el-tooltip>
            <el-tooltip content="设置" placement="bottom">
              <el-button circle size="large" class="action-btn">
                <el-icon><Setting /></el-icon>
              </el-button>
            </el-tooltip>
            <el-dropdown @command="handleCommand" class="user-dropdown">
              <div class="user-info">
                <el-avatar :size="36" class="user-avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="user-details">
                  <span class="user-name">{{ currentUser?.account || '管理员' }}</span>
                  <span class="user-role">超级管理员</span>
                </div>
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    账户设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边栏 -->
        <el-aside :width="sidebarCollapsed ? '64px' : '240px'" class="sidebar">
          <div class="sidebar-header">
            <el-button
              @click="toggleSidebar"
              circle
              size="small"
              class="collapse-btn"
              type="primary"
            >
              <el-icon><Fold v-if="!sidebarCollapsed" /><Expand v-else /></el-icon>
            </el-button>
          </div>
          <el-menu
            default-active="dashboard"
            class="sidebar-menu"
            :collapse="sidebarCollapsed"
            :collapse-transition="false"
          >
            <el-menu-item index="dashboard" class="menu-item">
              <el-icon><House /></el-icon>
              <template #title>仪表盘</template>
            </el-menu-item>
            <el-sub-menu index="users" class="menu-item">
              <template #title>
                <el-icon><User /></el-icon>
                <span>用户管理</span>
              </template>
              <el-menu-item index="users-list">用户列表</el-menu-item>
              <el-menu-item index="users-roles">角色权限</el-menu-item>
            </el-sub-menu>
            <el-sub-menu index="content" class="menu-item">
              <template #title>
                <el-icon><Document /></el-icon>
                <span>内容管理</span>
              </template>
              <el-menu-item index="content-list">内容列表</el-menu-item>
              <el-menu-item index="content-category">分类管理</el-menu-item>
            </el-sub-menu>
            <el-menu-item index="analytics" class="menu-item">
              <el-icon><TrendCharts /></el-icon>
              <template #title>数据分析</template>
            </el-menu-item>
            <el-menu-item index="settings" class="menu-item">
              <el-icon><Setting /></el-icon>
              <template #title>系统设置</template>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="main-content">
          <!-- 统计卡片区域 -->
          <div class="stats-grid">
            <el-card class="stats-card" shadow="hover">
              <div class="stats-content">
                <div class="stats-icon users-icon">
                  <el-icon size="24"><User /></el-icon>
                </div>
                <div class="stats-info">
                  <h3 class="stats-number">1,234</h3>
                  <p class="stats-label">总用户数</p>
                  <span class="stats-trend positive">
                    <el-icon><TrendCharts /></el-icon>
                    +12.5%
                  </span>
                </div>
              </div>
            </el-card>

            <el-card class="stats-card" shadow="hover">
              <div class="stats-content">
                <div class="stats-icon content-icon">
                  <el-icon size="24"><Document /></el-icon>
                </div>
                <div class="stats-info">
                  <h3 class="stats-number">5,678</h3>
                  <p class="stats-label">内容总数</p>
                  <span class="stats-trend positive">
                    <el-icon><TrendCharts /></el-icon>
                    +8.3%
                  </span>
                </div>
              </div>
            </el-card>

            <el-card class="stats-card" shadow="hover">
              <div class="stats-content">
                <div class="stats-icon revenue-icon">
                  <el-icon size="24"><Money /></el-icon>
                </div>
                <div class="stats-info">
                  <h3 class="stats-number">¥89,012</h3>
                  <p class="stats-label">本月收入</p>
                  <span class="stats-trend positive">
                    <el-icon><TrendCharts /></el-icon>
                    +15.2%
                  </span>
                </div>
              </div>
            </el-card>

            <el-card class="stats-card" shadow="hover">
              <div class="stats-content">
                <div class="stats-icon activity-icon">
                  <el-icon size="24"><DataLine /></el-icon>
                </div>
                <div class="stats-info">
                  <h3 class="stats-number">98.5%</h3>
                  <p class="stats-label">系统活跃度</p>
                  <span class="stats-trend positive">
                    <el-icon><TrendCharts /></el-icon>
                    +2.1%
                  </span>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 欢迎卡片 -->
          <div class="welcome-section">
            <el-card class="welcome-card" shadow="never">
              <div class="welcome-content">
                <div class="welcome-text">
                  <h2 class="welcome-title">欢迎回来，{{ currentUser?.account || '管理员' }}！</h2>
                  <p class="welcome-subtitle">今天是个美好的一天，让我们开始工作吧</p>
                  <div class="welcome-info">
                    <div class="info-item">
                      <span class="info-label">用户ID:</span>
                      <span class="info-value">{{ currentUser?.user_id }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">最后登录:</span>
                      <span class="info-value">{{ formatDate(new Date()) }}</span>
                    </div>
                  </div>
                </div>
                <div class="welcome-illustration">
                  <el-icon size="120" class="welcome-icon"><DataBoard /></el-icon>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 快速操作区域 -->
          <div class="quick-actions">
            <h3 class="section-title">快速操作</h3>
            <div class="actions-grid">
              <el-card class="action-card" shadow="hover">
                <div class="action-content">
                  <el-icon class="action-icon" size="32"><Plus /></el-icon>
                  <h4>添加用户</h4>
                  <p>快速添加新用户</p>
                </div>
              </el-card>
              <el-card class="action-card" shadow="hover">
                <div class="action-content">
                  <el-icon class="action-icon" size="32"><Edit /></el-icon>
                  <h4>内容管理</h4>
                  <p>管理系统内容</p>
                </div>
              </el-card>
              <el-card class="action-card" shadow="hover">
                <div class="action-content">
                  <el-icon class="action-icon" size="32"><View /></el-icon>
                  <h4>数据报表</h4>
                  <p>查看详细报表</p>
                </div>
              </el-card>
              <el-card class="action-card" shadow="hover">
                <div class="action-content">
                  <el-icon class="action-icon" size="32"><Setting /></el-icon>
                  <h4>系统设置</h4>
                  <p>配置系统参数</p>
                </div>
              </el-card>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User, ArrowDown, House, Document, DataBoard, Bell, Setting,
  SwitchButton, Fold, Expand, TrendCharts, Money, DataLine,
  Plus, Edit, View
} from '@element-plus/icons-vue'
import { getCurrentAdmin, adminLogout } from '@/api/auth'

const router = useRouter()

// 当前用户信息
const currentUser = ref(null)

// 侧边栏折叠状态
const sidebarCollapsed = ref(false)

// 获取当前用户信息
const loadCurrentUser = () => {
  currentUser.value = getCurrentAdmin()
}

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 格式化日期
const formatDate = (date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理下拉菜单命令
const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // 执行登出
      await adminLogout()
      ElMessage.success('已退出登录')

      // 跳转到登录页
      router.push('/login')
    } catch (error) {
      // 用户取消操作
      if (error !== 'cancel') {
        console.error('退出登录失败:', error)
      }
    }
  } else if (command === 'profile') {
    ElMessage.info('个人资料功能开发中...')
  } else if (command === 'settings') {
    ElMessage.info('账户设置功能开发中...')
  }
}

onMounted(() => {
  loadCurrentUser()
})
</script>

<style scoped>
.dashboard-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-text {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-btn {
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  color: #667eea;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.notification-badge {
  margin-right: 8px;
}

.user-dropdown {
  margin-left: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.user-info:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.user-avatar {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

.dropdown-icon {
  color: #666;
  transition: transform 0.3s ease;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 侧边栏样式 */
.sidebar {
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  overflow: hidden;
}

.sidebar-header {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.collapse-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.sidebar-menu {
  border: none;
  background: transparent;
  padding: 8px 0;
}

.menu-item {
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-menu-item) {
  color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  margin: 4px 12px;
  transition: all 0.3s ease;
}

:deep(.el-menu-item:hover) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
  color: #fff;
  transform: translateX(4px);
}

:deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

:deep(.el-sub-menu__title) {
  color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  margin: 4px 12px;
  transition: all 0.3s ease;
}

:deep(.el-sub-menu__title:hover) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
  color: #fff;
  transform: translateX(4px);
}

:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  color: #667eea;
}

/* 主内容区样式 */
.main-content {
  background: #f8fafc;
  padding: 24px;
  overflow-y: auto;
  min-height: calc(100vh - 60px);
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stats-card {
  border: none;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
}

.stats-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24px;
}

.users-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.content-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.revenue-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.activity-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #718096;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.stats-trend {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 8px;
}

.stats-trend.positive {
  color: #38a169;
  background: rgba(56, 161, 105, 0.1);
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 32px;
}

.welcome-card {
  border: none;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40px;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #fff;
}

.welcome-subtitle {
  font-size: 16px;
  margin: 0 0 24px 0;
  color: rgba(255, 255, 255, 0.9);
}

.welcome-info {
  display: flex;
  gap: 32px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
}

.welcome-illustration {
  opacity: 0.3;
}

.welcome-icon {
  color: rgba(255, 255, 255, 0.5);
}

/* 快速操作区域 */
.quick-actions {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 20px 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  border: none;
  border-radius: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fff;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.action-content {
  text-align: center;
  padding: 32px 20px;
}

.action-icon {
  color: #667eea;
  margin-bottom: 16px;
  background: rgba(102, 126, 234, 0.1);
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.action-content p {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }

  .header-center {
    display: none;
  }

  .main-content {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .welcome-content {
    flex-direction: column;
    text-align: center;
    padding: 32px 24px;
  }

  .welcome-illustration {
    margin-top: 20px;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}
</style>
