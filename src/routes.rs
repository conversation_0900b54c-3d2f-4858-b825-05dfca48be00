use actix_web::web;

/**
 * 所有 API 路由配置
 *
 * 此文件集中管理所有 API 路由，方便快速查看项目中的所有接口
 */

/// 用户相关路由
pub fn user_routes(cfg: &mut web::ServiceConfig) {
    // 用户注册
    cfg.route("/users/regist", web::post().to(crate::handlers::user::register::handle));

    // 用户登录
    cfg.route("/users/login", web::post().to(crate::handlers::user::login::handle));

    // 用户列表
    cfg.route("/users/list", web::get().to(crate::handlers::user::list::handle));
}

/// 收藏夹相关路由
pub fn favorite_routes(cfg: &mut web::ServiceConfig) {
    // 创建收藏夹
    cfg.route("/favorites/create", web::post().to(crate::handlers::favorite::create::handle));

    // 获取收藏夹列表
    cfg.route("/favorites/list", web::get().to(crate::handlers::favorite::list::handle));

    // 更新收藏夹
    cfg.route("/favorites/update", web::post().to(crate::handlers::favorite::update::handle));

    // 更新收藏夹排序
    cfg.route("/favorites/update_order", web::post().to(crate::handlers::favorite::update_order::handle));

    // 交换收藏夹位置
    cfg.route("/favorites/swap", web::post().to(crate::handlers::favorite::swap::handle));

    // 删除收藏夹
    cfg.route("/favorites/delete", web::post().to(crate::handlers::favorite::delete::handle));

    // 其他收藏夹相关路由可以在这里添加
}

/// 书签相关路由
pub fn bookmark_routes(cfg: &mut web::ServiceConfig) {
    // 添加书签
    cfg.route("/bookmark/add", web::post().to(crate::handlers::bookmark::add::handle));

    // 获取书签列表 (支持GET和POST两种方法)
    cfg.route("/bookmark/list", web::post().to(crate::handlers::bookmark::list::handle));
    cfg.route("/bookmark/list", web::get().to(crate::handlers::bookmark::list::handle_get));

    // 更新书签
    cfg.route("/bookmark/update", web::post().to(crate::handlers::bookmark::update::handle));

    // 删除书签
    cfg.route("/bookmark/delete", web::post().to(crate::handlers::bookmark::delete::handle));

    // 搜索书签
    cfg.route("/bookmark/search", web::get().to(crate::handlers::bookmark::search::handle));

    // 书签标签管理
    // 给书签添加标签
    cfg.route("/bookmark/tags/add", web::post().to(crate::handlers::bookmark::add_tags::handle));

    // 从书签移除标签
    cfg.route("/bookmark/tags/remove", web::post().to(crate::handlers::bookmark::remove_tags::handle));

    // 获取书签标签
    cfg.route("/bookmark/tags", web::get().to(crate::handlers::bookmark::get_tags::handle));
}

/// 标签相关路由
pub fn tag_routes(cfg: &mut web::ServiceConfig) {
    // 获取标签列表
    cfg.route("/tags/list", web::get().to(crate::handlers::tag::list::handle));

    // 创建标签
    cfg.route("/tags/create", web::post().to(crate::handlers::tag::create::handle));

    // 删除标签
    cfg.route("/tags/delete", web::post().to(crate::handlers::tag::delete::handle));
}

/// 文件上传相关路由
pub fn upload_routes(cfg: &mut web::ServiceConfig) {
    // 上传文件接口（需要登录验证）
    cfg.route("/upload/file", web::post().to(crate::handlers::upload::file::handle));

    // 上传文件接口（无需登录验证）
    cfg.route("/upload/direct", web::post().to(crate::handlers::upload::direct::handle));
}

/// OSS相关路由
pub fn oss_routes(cfg: &mut web::ServiceConfig) {
    // 获取STS临时授权Token
    cfg.route("/oss/sts/token", web::get().to(crate::handlers::oss::sts::token::handle));
}

/// 微信相关路由
pub fn wechat_routes(cfg: &mut web::ServiceConfig) {
    // 微信登录接口
    cfg.route("/wechat/login", web::post().to(crate::handlers::wechat::login_handle));
}

/// 腾讯云相关路由
pub fn tencent_cloud_routes(cfg: &mut web::ServiceConfig) {
    // 创建语音识别任务接口
    cfg.route("/tencent_cloud/asr/create_task", web::post().to(crate::handlers::tencent_cloud::asr::create_rec_task));

    // 腾讯云语音识别回调接口
    cfg.route("/tencent_cloud/asr/callback", web::post().to(crate::handlers::tencent_cloud::callback::handle_asr_callback));
}

/// 测试相关路由 - 用于性能测试，不影响业务逻辑
pub fn test_routes(cfg: &mut web::ServiceConfig) {
    // 简单的 ping 测试接口
    cfg.route("/test/ping", web::get().to(crate::handlers::test::ping::handle));

    // MNS消息发送测试接口
    cfg.route("/test/mns", web::post().to(crate::handlers::test::mns::handle));

    // MySQL连接测试接口
    cfg.route("/test/mysql", web::get().to(crate::handlers::test::mysql::handle));

    // MySQL服务测试接口
    cfg.route("/test/mysql_services", web::get().to(crate::handlers::test::mysql_services::handle));

    // 腾讯云配置测试接口
    cfg.route("/test/tencent_cloud_config", web::get().to(crate::handlers::test::tencent_cloud_config::handle));

    // 腾讯云语音识别服务测试接口
    cfg.route("/test/tencent_cloud_asr", web::get().to(crate::handlers::test::tencent_cloud_asr::handle));
}

/// 应用版本相关路由
pub fn app_version_routes(cfg: &mut web::ServiceConfig) {
    // 版本检查接口
    cfg.route("/app/version/check", web::get().to(crate::handlers::app_version::check::handle));

    // 版本存储接口
    cfg.route("/app/version/store", web::post().to(crate::handlers::app_version::store::handle));
}

/// LLM相关路由
pub fn llm_routes(cfg: &mut web::ServiceConfig) {
    // LLM聊天接口
    cfg.route("/llm/chat", web::post().to(crate::handlers::llm::chat::handle));
}

/// 笔记相关路由
pub fn note_routes(cfg: &mut web::ServiceConfig) {
    // 创建笔记
    cfg.route("/note/create", web::post().to(crate::handlers::note::create_note));

    // 获取笔记详情
    cfg.route("/note/detail", web::get().to(crate::handlers::note::get_note_detail));

    // 获取笔记列表
    cfg.route("/note/list", web::get().to(crate::handlers::note::list::handle));

    // 更新笔记
    cfg.route("/note/update", web::post().to(crate::handlers::note::update_note));

    // 删除笔记
    cfg.route("/note/delete", web::post().to(crate::handlers::note::delete_note));
}

/// 笔记草稿相关路由
pub fn note_draft_routes(cfg: &mut web::ServiceConfig) {
    // 创建笔记草稿
    cfg.route("/note/draft/create", web::post().to(crate::handlers::note_draft::create::handle));

    // 获取笔记草稿详情
    cfg.route("/note/draft/detail", web::get().to(crate::handlers::note_draft::detail::handle));

    // 获取笔记草稿列表
    cfg.route("/note/draft/list", web::get().to(crate::handlers::note_draft::list::handle));

    // 更新笔记草稿
    cfg.route("/note/draft/update", web::post().to(crate::handlers::note_draft::update::handle));

    // 删除笔记草稿
    cfg.route("/note/draft/delete", web::post().to(crate::handlers::note_draft::delete::handle));
}

/// 素材相关路由
pub fn material_routes(cfg: &mut web::ServiceConfig) {
    // 添加素材
    cfg.route("/material/add", web::post().to(crate::handlers::material::add::handle));

    // 删除素材
    cfg.route("/material/delete", web::post().to(crate::handlers::material::delete::handle));

    // 获取素材列表
    cfg.route("/material/list", web::get().to(crate::handlers::material::list::handle));
}

/// Augment用户相关路由
pub fn augment_user_routes(cfg: &mut web::ServiceConfig) {
    // Augment用户登录
    cfg.route("/augment/users/login", web::post().to(crate::handlers::augment_user::login::handle));
}

/// 任务相关路由
pub fn task_routes(cfg: &mut web::ServiceConfig) {
    // 创建任务
    cfg.route("/task/create", web::post().to(crate::handlers::task::create::handle));

    // 获取任务列表
    cfg.route("/task/list", web::get().to(crate::handlers::task::list::handle));

    // 查询任务状态
    cfg.route("/task/status", web::get().to(crate::handlers::task::status::handle));

    // 查询用户是否有创建笔记任务
    cfg.route("/task/note/check", web::get().to(crate::handlers::task::note_status::check_create_note_task));

    // 取消用户的创建笔记任务
    cfg.route("/task/note/cancel", web::get().to(crate::handlers::task::note_status::cancel_create_note_task));
}

/// 提示词相关路由
pub fn prompt_routes(cfg: &mut web::ServiceConfig) {
    // 创建提示词
    cfg.route("/prompt/create", web::post().to(crate::handlers::prompt::create::handle));

    // 获取提示词列表
    cfg.route("/prompt/list", web::get().to(crate::handlers::prompt::list::handle));

    // 获取提示词详情
    cfg.route("/prompt/detail", web::get().to(crate::handlers::prompt::detail::handle));

    // 更新提示词
    cfg.route("/prompt/update", web::post().to(crate::handlers::prompt::update::handle));

    // 删除提示词
    cfg.route("/prompt/delete", web::post().to(crate::handlers::prompt::delete::handle));
}

/// 系统提示词相关路由
pub fn system_prompt_routes(cfg: &mut web::ServiceConfig) {
    // 创建系统提示词
    cfg.route("/system_prompt/create", web::post().to(crate::handlers::system_prompt::create::handle));

    // 获取系统提示词列表
    cfg.route("/system_prompt/list", web::get().to(crate::handlers::system_prompt::list::handle));

    // 获取系统提示词详情
    cfg.route("/system_prompt/detail", web::get().to(crate::handlers::system_prompt::detail::handle));

    // 更新系统提示词
    cfg.route("/system_prompt/update", web::post().to(crate::handlers::system_prompt::update::handle));

    // 删除系统提示词
    cfg.route("/system_prompt/delete", web::post().to(crate::handlers::system_prompt::delete::handle));
}

/// AI使用次数相关路由
pub fn ai_usage_routes(cfg: &mut web::ServiceConfig) {
    // 创建AI使用记录
    cfg.route("/ai-usage/create", web::post().to(crate::handlers::ai_usage::create::handle));

    // 获取用户AI使用次数
    cfg.route("/ai-usage/get", web::get().to(crate::handlers::ai_usage::get::handle));
}

/// 后台管理用户相关路由
pub fn admin_user_routes(cfg: &mut web::ServiceConfig) {
    // 后台管理用户登录
    cfg.route("/admin/users/login", web::post().to(crate::handlers::admin_user::login::handle));
}

/// 后台管理普通用户相关路由
pub fn admin_manage_user_routes(cfg: &mut web::ServiceConfig) {
    // 获取用户列表
    cfg.route("/users/list", web::get().to(crate::handlers::user::list::handle));
}



/// 配置所有路由
pub fn configure_routes(cfg: &mut web::ServiceConfig) {
    // 用户相关路由
    user_routes(cfg);

    // 收藏夹相关路由
    favorite_routes(cfg);

    // 书签相关路由
    bookmark_routes(cfg);

    // 标签相关路由
    tag_routes(cfg);

    // 文件上传相关路由
    upload_routes(cfg);

    // OSS相关路由
    oss_routes(cfg);

    // 微信相关路由
    wechat_routes(cfg);

    // 腾讯云相关路由
    tencent_cloud_routes(cfg);

    // 应用版本相关路由
    app_version_routes(cfg);

    // LLM相关路由
    llm_routes(cfg);

    // 笔记相关路由
    note_routes(cfg);

    // 笔记草稿相关路由
    note_draft_routes(cfg);

    // 素材相关路由
    material_routes(cfg);

    // Augment用户相关路由
    augment_user_routes(cfg);

    // 任务相关路由
    task_routes(cfg);

    // 提示词相关路由
    prompt_routes(cfg);

    // 系统提示词相关路由
    system_prompt_routes(cfg);

    // AI使用次数相关路由
    ai_usage_routes(cfg);

    // 后台管理用户相关路由
    admin_user_routes(cfg);

    // 测试相关路由
    test_routes(cfg);
}
